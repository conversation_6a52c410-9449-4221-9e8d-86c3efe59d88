import React, { useEffect, useState, useRef, lazy, Suspense } from "react";
import Header from "@/components/Header";
import { SearchSection } from "@/components/home/<USER>";
import { HomepageSEO } from "@/components/seo/HomepageSEO";
import { useHomepageData } from "@/hooks/useHomepageData";

// Lazy load componentes não críticos para melhor LCP
const Footer = lazy(() => import("@/components/Footer"));
const CategoryList = lazy(() => import("@/components/home/<USER>").then(module => ({ default: module.CategoryList })));
const UnifiedContentBadge = lazy(() => import("@/components/home/<USER>").then(module => ({ default: module.UnifiedContentBadge })));
const SiteFeedbackDialog = lazy(() => import("@/components/feedback/SiteFeedbackDialog").then(module => ({ default: module.SiteFeedbackDialog })));
const AndroidInstallDialog = lazy(() => import("@/components/mobile/AndroidInstallDialog").then(module => ({ default: module.AndroidInstallDialog })));
const MobileThemeToggle = lazy(() => import("@/components/theme/MobileThemeToggle"));

const Index: React.FC = () => {
  const [searchPlaceholder, setSearchPlaceholder] = useState("");
  const [isMobile, setIsMobile] = useState(false);
  // Removido: estado bgImageLoaded desnecessário

  const placeholders = [
    "Buscar por Amoxicilina...",
    "Buscar por Dipirona...",
    "Buscar por Prednisolona...",
    "Buscar por Salbutamol...",
    "Buscar por Asma.."
  ];

  // Buscar dados para SEO e estatísticas
  const { data: homepageData, isLoading } = useHomepageData();

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Placeholder estático para melhor LCP - animação removida para performance
  useEffect(() => {
    // Definir placeholder estático após 1 segundo para não afetar LCP
    const timeout = setTimeout(() => {
      setSearchPlaceholder("Buscar medicamentos, condutas, calculadoras...");
    }, 1000);

    return () => clearTimeout(timeout);
  }, []);

  // Removido: pré-carregamento de imagem de fundo desnecessária
  // O site usa apenas gradientes CSS para o fundo

  return (
    <div className="min-h-screen flex flex-col relative">
      {/* Fundo com gradiente suave para aparência de app */}
      <div
        className="fixed inset-0 w-full h-full pointer-events-none z-0 bg-gradient-to-b from-blue-50/80 to-white dark:from-slate-900/90 dark:to-slate-800/80"
      />

      {/* Padrão de fundo sutil */}
      <div
        className="fixed inset-0 w-full h-full pointer-events-none z-0 opacity-5 dark:opacity-10"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231E40AF' fill-opacity='0.15'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}
      />

      {/* Botão de tema removido do topo - agora apenas no canto inferior esquerdo */}

      {/* SEO Dinâmico com dados do banco */}
      <HomepageSEO
        categories={homepageData?.categories}
      />

      <Header hideSearch />

      <main className="flex-1 container mx-auto px-4 py-4 md:py-6 relative z-20 pb-24 sm:pb-0">
        <div className="space-y-2 animate-fade-in-up">
          {/* Título e descrição - visíveis apenas em desktop */}
          <div className="text-center space-y-1 mb-4 hidden md:block">
            <h1
              className="text-4xl md:text-5xl font-bold text-center bg-gradient-to-r from-primary to-blue-600 text-transparent bg-clip-text"
              data-text="PedBook"
            >
              PedBook
            </h1>
            <p className="text-sm md:text-base text-gray-600 dark:text-gray-300">
              Calculadora de Doses Pediátricas
            </p>
          </div>

          <SearchSection searchPlaceholder={searchPlaceholder} />

          {/* Badge unificada de conteúdo - lazy loaded */}
          <div className="py-2 animate-fade-in" style={{ animationDelay: '300ms' }}>
            <Suspense fallback={<div className="h-8 bg-gray-100 dark:bg-gray-800 rounded animate-pulse" />}>
              <UnifiedContentBadge />
            </Suspense>
          </div>

          {/* CategoryList - lazy loaded */}
          <Suspense fallback={
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 animate-pulse">
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="h-24 bg-gray-100 dark:bg-gray-800 rounded-lg" />
              ))}
            </div>
          }>
            <CategoryList />
          </Suspense>
        </div>
      </main>

      {/* FloatingSupport agora é lazy loaded no App.tsx */}
      <Suspense fallback={null}>
        <MobileThemeToggle />
      </Suspense>

      <div className="hidden sm:block relative z-10">
        <Suspense fallback={<div className="h-16 bg-gray-100 dark:bg-gray-800" />}>
          <Footer />
        </Suspense>
      </div>

      <Suspense fallback={null}>
        <SiteFeedbackDialog />
      </Suspense>

      <Suspense fallback={null}>
        <AndroidInstallDialog />
      </Suspense>
    </div>
  );
};

export default Index;
