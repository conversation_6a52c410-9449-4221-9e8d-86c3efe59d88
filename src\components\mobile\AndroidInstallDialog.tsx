
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";

const STORAGE_KEY = "android_install_dialog_shown";

export function AndroidInstallDialog() {
  const [isOpen, setIsOpen] = useState(false);
  const [isAndroid, setIsAndroid] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  useEffect(() => {
    const checkPlatform = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const wasShown = localStorage.getItem(STORAGE_KEY);



      // Detecção simplificada de Android Mobile
      const isRealAndroidMobile = () => {
        // Verificações básicas
        const hasAndroidUA = /android/i.test(userAgent);
        const isMobile = /mobile/i.test(userAgent);
        const hasTouch = 'ontouchstart' in window;
        const isSmallScreen = window.screen.width <= 768; // Aumentado para capturar mais dispositivos

        // Exclusões básicas
        const isIOS = /iphone|ipad|ipod/i.test(userAgent);
        const isDesktop = /windows nt|linux x86_64|x11.*linux|macintosh/i.test(userAgent);
        const isSimulation = navigator.webdriver === true;



        // Deve ser Android mobile e não ser iOS/Desktop/Simulação
        return hasAndroidUA && isMobile && hasTouch && isSmallScreen && !isIOS && !isDesktop && !isSimulation;
      };

      // Detecção simplificada de Play Store
      const isFromPlayStore = () => {
        const referrer = document.referrer.toLowerCase();
        const hasPlayStoreReferrer = /play\.google\.com|googleplay/.test(referrer);
        const hasPlayStoreParams = window.location.search.includes('utm_source=play');



        return hasPlayStoreReferrer || hasPlayStoreParams;
      };

      // Detecção simplificada de WebView/App
      const isInAppEnvironment = () => {
        const isWebView = /wv/.test(userAgent) || /WebView/.test(userAgent);
        const hasAppProps = 'AndroidInterface' in window || 'android' in window;



        return isWebView || hasAppProps;
      };

      const isAndroidMobile = isRealAndroidMobile();
      const isFromStore = isFromPlayStore();
      const isInApp = isInAppEnvironment();

      const shouldShow = isAndroidMobile && !isStandalone && !wasShown && !isFromStore && !isInApp;

      setIsAndroid(isAndroidMobile);
      setIsOpen(shouldShow);
    };

    // Aguardar 5 segundos para melhorar LCP e não interromper o usuário imediatamente
    setTimeout(checkPlatform, 5000);

    // Preload da imagem em background após 3 segundos para que esteja pronta quando o dialog abrir
    setTimeout(() => {
      const img = new Image();
      img.onload = () => setImageLoaded(true);
      img.src = "https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo//google-play-download-android-app-logo-png-transparent.png";
    }, 3000);
  }, []);

  const handleClose = () => {
    localStorage.setItem(STORAGE_KEY, "true");
    setIsOpen(false);
  };

  const handleDownload = () => {
    window.open("https://play.google.com/store/apps/details?id=com.med.pedbook&hl=pt_BR", "_blank");
    handleClose();
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-[90dvw] max-h-[90dvh] w-[400px] rounded-2xl border-none bg-white/90 backdrop-blur-sm shadow-lg">
          <DialogHeader className="space-y-3">
            <DialogTitle className="text-2xl font-bold text-primary">Instale o PedBook</DialogTitle>
            <DialogDescription className="text-base">
              Baixe nosso aplicativo para ter acesso rápido e fácil ao PedBook diretamente do seu celular!
            </DialogDescription>
          </DialogHeader>

          <div className="flex flex-col gap-6 items-center py-4">
            {/* Lazy load da imagem apenas quando o dialog estiver aberto */}
            {isOpen && (
              <div className="w-48 h-16 flex items-center justify-center">
                {imageLoaded ? (
                  <img
                    src="https://bxedpdmgvgatjdfxgxij.supabase.co/storage/v1/object/public/logo//google-play-download-android-app-logo-png-transparent.png"
                    alt="Google Play Store"
                    className="w-48 h-auto object-contain hover:scale-105 transition-transform"
                    loading="lazy"
                  />
                ) : (
                  <div className="w-48 h-16 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <Download className="h-8 w-8 text-gray-400 animate-pulse" />
                  </div>
                )}
              </div>
            )}

            <div className="flex gap-3 w-full">
              <Button
                variant="outline"
                className="flex-1 rounded-xl hover:bg-gray-100"
                onClick={handleClose}
              >
                Prefiro não
              </Button>

              <Button
                className="flex-1 gap-2 rounded-xl bg-primary hover:bg-primary/90"
                onClick={handleDownload}
              >
                <Download className="h-4 w-4" />
                Instalar
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
