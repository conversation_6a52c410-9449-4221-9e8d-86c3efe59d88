
import React from 'react';
import { CommandEmpty, CommandList } from "@/components/ui/command";
import { SearchResult } from "./types";
import { Loader2, Pill, FileText, Calculator, Activity, Baby, Syringe, BookOpen, AlertTriangle, Shield, X } from "lucide-react";
import { SearchResultGroup } from './components/SearchResultGroup';
import { EmptyResults } from './components/EmptyResults';
import { DrWillSuggestion } from './components/DrWillSuggestion';

interface SearchResultsProps {
  results: SearchResult[];
  onSelect: (result: SearchResult) => void;
  formatBrands: (brands: string | null | undefined) => string;
  isLoading?: boolean;
  isFetching?: boolean;
  showEmptyMessage?: boolean;
  searchTerm?: string;
  currentInput?: string; // Para mostrar o que o usuário está digitando
  onClose?: () => void; // Callback para fechar o search
}

export const SearchResults = React.memo(({
  results,
  onSelect,
  formatBrands,
  isLoading,
  isFetching,
  showEmptyMessage,
  searchTerm,
  currentInput,
  onClose
}: SearchResultsProps) => {

  // Verificar se há apenas resultados fuzzy
  const hasOnlyFuzzyResults = results.length > 0 && results.every(result => result.isFuzzyMatch);
  const shouldShowDrWill = hasOnlyFuzzyResults && searchTerm && searchTerm.length >= 3;





  // Se o usuário está digitando mas ainda não tem 3 caracteres
  if (currentInput && currentInput.length > 0 && currentInput.length < 3) {
    return (
      <div className="p-4 text-center">
        <div className="flex items-center justify-center gap-2 text-muted-foreground">
          <div className="w-2 h-2 bg-primary/60 rounded-full animate-pulse" />
          <p className="text-sm">
            Digite mais {3 - currentInput.length} caractere{3 - currentInput.length > 1 ? 's' : ''} para buscar...
          </p>
        </div>
        <div className="mt-2 text-xs text-muted-foreground">
          Buscando por: "<span className="font-medium">{currentInput}</span>"
        </div>
      </div>
    );
  }
  if (showEmptyMessage) {
    return (
      <div className="p-4 space-y-4 relative">
        {/* Botão de fechar */}
        {onClose && (
          <button
            onClick={onClose}
            className="absolute top-2 right-2 h-6 w-6 rounded-full bg-gray-100/80 dark:bg-slate-700/80 hover:bg-gray-200 dark:hover:bg-slate-600 flex items-center justify-center transition-colors"
            title="Fechar busca"
          >
            <X className="h-3 w-3 text-gray-500 dark:text-gray-400" />
          </button>
        )}

        <div className="text-center text-muted-foreground mb-4">
          <p className="text-sm">Digite pelo menos 3 caracteres para começar a buscar...</p>
        </div>

        {/* Sugestões rápidas */}
        <div className="space-y-3">
          <div className="text-xs font-medium text-muted-foreground px-2">Sugestões populares:</div>
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => onSelect({ id: 'paracetamol', name: 'Paracetamol', type: 'medication', slug: 'paracetamol' })}
              className="flex items-center gap-2 p-2 text-left text-xs bg-primary/5 hover:bg-primary/10 rounded-lg transition-colors"
            >
              <Pill className="h-3 w-3 text-primary" />
              Paracetamol
            </button>
            <button
              onClick={() => onSelect({ id: 'dipirona', name: 'Dipirona', type: 'medication', slug: 'dipirona' })}
              className="flex items-center gap-2 p-2 text-left text-xs bg-primary/5 hover:bg-primary/10 rounded-lg transition-colors"
            >
              <Pill className="h-3 w-3 text-primary" />
              Dipirona
            </button>
            <button
              onClick={() => onSelect({ id: 'apgar', name: 'Calculadora de Apgar', type: 'calculator', path: '/calculadoras/apgar' })}
              className="flex items-center gap-2 p-2 text-left text-xs bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
            >
              <Calculator className="h-3 w-3 text-blue-500" />
              Apgar
            </button>
            <button
              onClick={() => onSelect({ id: 'glasgow', name: 'Calculadora de Glasgow', type: 'calculator', path: '/calculadoras/glasgow' })}
              className="flex items-center gap-2 p-2 text-left text-xs bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
            >
              <Calculator className="h-3 w-3 text-blue-500" />
              Glasgow
            </button>
          </div>
        </div>

        {/* Dica de busca */}
        <div className="text-center pt-2 border-t border-border/50">
          <p className="text-xs text-muted-foreground">
            💡 Busque por medicamentos, calculadoras, condutas ou CIDs
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="p-6 text-center">
        <Loader2 className="h-6 w-6 animate-spin text-primary/60 mx-auto" />
        <p className="text-sm text-muted-foreground mt-2">Buscando resultados...</p>
      </div>
    );
  }

  // APENAS mostrar EmptyResults se:
  // 1. Não está carregando NEM fazendo fetch
  // 2. Tem termo de busca válido (≥3 chars)
  // 3. Não há resultados
  // 4. O searchTerm é igual ao currentInput (busca finalizada)
  const shouldShowEmptyResults = !isLoading &&
                                !isFetching && // CORREÇÃO PRINCIPAL: usar isFetching
                                searchTerm &&
                                searchTerm.length >= 3 &&
                                results.length === 0 &&
                                searchTerm === currentInput;

  if (shouldShowEmptyResults) {
    return <EmptyResults searchTerm={searchTerm} />;
  }

  // Se searchTerm !== currentInput OU está fazendo fetch, significa que está processando
  if ((searchTerm !== currentInput && currentInput && currentInput.length >= 3) ||
      (isFetching && searchTerm && searchTerm.length >= 3)) {
    return (
      <div className="p-6 text-center">
        <Loader2 className="h-6 w-6 animate-spin text-primary/60 mx-auto" />
        <p className="text-sm text-muted-foreground mt-2">Buscando resultados...</p>
      </div>
    );
  }

  // Se há resultados, mostrar normalmente

  const medications = results.filter(result => result.type === 'medication');
  const categories = results.filter(result => result.type === 'category');
  const calculators = results.filter(result => result.type === 'calculator');
  const flowcharts = results.filter(result => result.type === 'flowchart');
  const childcareItems = results.filter(result => result.type === 'childcare');
  const toxidromes = results.filter(result => result.type === 'toxidrome');
  const icd10Results = results.filter(result => result.type === 'icd10');
  const conductResults = results.filter(result => result.type === 'conduct');
  const vaccines = results.filter(result => result.type === 'vaccine');

  return (
    <CommandList className="max-h-[60vh] overflow-y-auto rounded-lg bg-transparent">
      <SearchResultGroup
        title="Condutas e Manejos"
        results={conductResults}
        onSelect={onSelect}
        icon={<BookOpen className="h-4 w-4 text-emerald-600" />}
      />
      <SearchResultGroup
        title="Vacinas"
        results={vaccines}
        onSelect={onSelect}
        icon={<Shield className="h-4 w-4 text-purple-500" />}
      />
      <SearchResultGroup
        title="Medicamentos"
        results={medications}
        onSelect={onSelect}
        formatBrands={formatBrands}
        icon={<Pill className="h-4 w-4 text-primary" />}
      />
      <SearchResultGroup
        title="Intoxicações"
        results={toxidromes}
        onSelect={onSelect}
        icon={<AlertTriangle className="h-4 w-4 text-amber-500" />}
      />
      <SearchResultGroup
        title="Calculadoras"
        results={calculators}
        onSelect={onSelect}
        icon={<Calculator className="h-4 w-4 text-blue-500" />}
      />
      <SearchResultGroup
        title="Fluxogramas"
        results={flowcharts}
        onSelect={onSelect}
        icon={<Activity className="h-4 w-4 text-indigo-500" />}
      />
      <SearchResultGroup
        title="Puericultura"
        results={childcareItems}
        onSelect={onSelect}
        icon={<Baby className="h-4 w-4 text-pink-500" />}
      />
      <SearchResultGroup
        title="Categorias"
        results={categories}
        onSelect={onSelect}
        icon={<FileText className="h-4 w-4 text-gray-600" />}
      />
      <SearchResultGroup
        title="CID-10"
        results={icd10Results}
        onSelect={onSelect}
        icon={<FileText className="h-4 w-4 text-cyan-500" />}
      />

      {/* Seção do Dr. Will - aparece apenas quando há só resultados fuzzy */}
      {shouldShowDrWill && (
        <DrWillSuggestion
          searchTerm={searchTerm}
          onSelect={onClose}
        />
      )}
    </CommandList>
  );
});
