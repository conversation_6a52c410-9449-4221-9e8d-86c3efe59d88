import Fuse from 'fuse.js';

// Função para calcular distância de Levenshtein (similaridade entre strings)
export const levenshteinDistance = (str1: string, str2: string): number => {
  const matrix = [];
  const len1 = str1.length;
  const len2 = str2.length;

  // Inicializar matriz
  for (let i = 0; i <= len2; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= len1; j++) {
    matrix[0][j] = j;
  }

  // Calcular distância
  for (let i = 1; i <= len2; i++) {
    for (let j = 1; j <= len1; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1, // substituição
          matrix[i][j - 1] + 1,     // inserção
          matrix[i - 1][j] + 1      // deleção
        );
      }
    }
  }

  return matrix[len2][len1];
};

// Função para calcular similaridade percentual
export const calculateSimilarity = (str1: string, str2: string): number => {
  const maxLength = Math.max(str1.length, str2.length);
  if (maxLength === 0) return 1;
  
  const distance = levenshteinDistance(str1.toLowerCase(), str2.toLowerCase());
  return (maxLength - distance) / maxLength;
};

// Configuração do Fuse.js para busca fuzzy
export const createFuseInstance = <T>(data: T[], keys: string[]) => {
  return new Fuse(data, {
    keys,
    threshold: 0.4, // 0 = match exato, 1 = match qualquer coisa
    distance: 100,  // distância máxima para considerar match
    minMatchCharLength: 2,
    includeScore: true,
    includeMatches: true,
    ignoreLocation: true,
    findAllMatches: true,
    useExtendedSearch: true
  });
};

// Função para normalizar texto removendo acentos e caracteres especiais
export const normalizeForFuzzy = (text: string): string => {
  return text
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove acentos
    .replace(/[^a-z0-9\s]/g, '') // Remove caracteres especiais
    .trim();
};

// Função para gerar variações comuns de digitação
export const generateTypingVariations = (term: string): string[] => {
  const variations = [term];
  const normalized = normalizeForFuzzy(term);
  
  // Adicionar versão normalizada
  if (normalized !== term.toLowerCase()) {
    variations.push(normalized);
  }
  
  // Variações comuns de erros de digitação
  const commonMistakes = [
    // Trocar letras adjacentes
    term.replace(/([a-z])([a-z])/g, '$2$1'),
    // Remover primeira letra
    term.substring(1),
    // Remover última letra
    term.substring(0, term.length - 1),
    // Duplicar letras comuns
    term.replace(/([aeiou])/g, '$1$1'),
    // Trocar 'ph' por 'f'
    term.replace(/ph/g, 'f'),
    // Trocar 'k' por 'c'
    term.replace(/k/g, 'c'),
    // Trocar 'y' por 'i'
    term.replace(/y/g, 'i'),
  ];
  
  variations.push(...commonMistakes.filter(v => v && v !== term));
  
  return [...new Set(variations)]; // Remove duplicatas
};

// Interface para resultado de busca fuzzy
export interface FuzzySearchResult<T> {
  item: T;
  score: number;
  matches?: Fuse.FuseResultMatch[];
}

// Função principal de busca fuzzy
export const performFuzzySearch = <T>(
  data: T[],
  searchTerm: string,
  keys: string[],
  maxResults: number = 10,
  threshold: number = 0.6
): FuzzySearchResult<T>[] => {
  if (!searchTerm || searchTerm.length < 2) return [];

  const fuse = createFuseInstance(data, keys);
  const variations = generateTypingVariations(searchTerm);

  const allResults: FuzzySearchResult<T>[] = [];

  // Buscar por cada variação
  variations.forEach(variation => {
    const results = fuse.search(variation);
    results.forEach(result => {
      if (result.score !== undefined && result.score < threshold) { // Score menor = melhor match
        allResults.push({
          item: result.item,
          score: result.score,
          matches: result.matches
        });
      }
    });
  });

  // Remover duplicatas e ordenar por score
  const uniqueResults = allResults
    .filter((result, index, self) =>
      index === self.findIndex(r => JSON.stringify(r.item) === JSON.stringify(result.item))
    )
    .sort((a, b) => a.score - b.score) // Score menor = melhor
    .slice(0, maxResults);

  return uniqueResults;
};

// Função específica para busca fuzzy de múltiplos tipos
export const performMultiTypeFuzzySearch = (
  allData: {
    medications: any[];
    categories: any[];
    calculators: any[];
    flowcharts: any[];
    childcare: any[];
    conducts: any[];
    vaccines: any[];
    icd10: any[];
  },
  searchTerm: string,
  maxResultsPerType: number = 3
) => {
  const results = {
    medications: [] as any[],
    categories: [] as any[],
    calculators: [] as any[],
    flowcharts: [] as any[],
    childcare: [] as any[],
    conducts: [] as any[],
    vaccines: [] as any[],
    icd10: [] as any[]
  };

  // Busca fuzzy para medicamentos
  if (allData.medications.length > 0) {
    const fuzzyMeds = performFuzzySearch(
      allData.medications,
      searchTerm,
      ['name', 'brands'],
      maxResultsPerType,
      0.6
    );
    results.medications = fuzzyMeds.map(r => r.item);
  }

  // Busca fuzzy para categorias
  if (allData.categories.length > 0) {
    const fuzzyCats = performFuzzySearch(
      allData.categories,
      searchTerm,
      ['name'],
      maxResultsPerType,
      0.5
    );
    results.categories = fuzzyCats.map(r => r.item);
  }

  // Busca fuzzy para calculadoras
  if (allData.calculators.length > 0) {
    const fuzzyCalcs = performFuzzySearch(
      allData.calculators,
      searchTerm,
      ['name', 'description'],
      maxResultsPerType,
      0.5
    );
    results.calculators = fuzzyCalcs.map(r => r.item);
  }

  // Busca fuzzy para fluxogramas
  if (allData.flowcharts.length > 0) {
    const fuzzyFlows = performFuzzySearch(
      allData.flowcharts,
      searchTerm,
      ['name', 'description'],
      maxResultsPerType,
      0.5
    );
    results.flowcharts = fuzzyFlows.map(r => r.item);
  }

  // Busca fuzzy para puericultura
  if (allData.childcare.length > 0) {
    const fuzzyChild = performFuzzySearch(
      allData.childcare,
      searchTerm,
      ['name', 'description'],
      maxResultsPerType,
      0.5
    );
    results.childcare = fuzzyChild.map(r => r.item);
  }

  // Busca fuzzy para condutas
  if (allData.conducts.length > 0) {
    const fuzzyCond = performFuzzySearch(
      allData.conducts,
      searchTerm,
      ['name'],
      maxResultsPerType,
      0.5
    );
    results.conducts = fuzzyCond.map(r => r.item);
  }

  // Busca fuzzy para vacinas
  if (allData.vaccines.length > 0) {
    const fuzzyVaccines = performFuzzySearch(
      allData.vaccines,
      searchTerm,
      ['name'],
      maxResultsPerType,
      0.5
    );
    results.vaccines = fuzzyVaccines.map(r => r.item);
  }

  // Busca fuzzy para CID10
  if (allData.icd10.length > 0) {
    const fuzzyIcd = performFuzzySearch(
      allData.icd10,
      searchTerm,
      ['name', 'code_range'],
      maxResultsPerType,
      0.5
    );
    results.icd10 = fuzzyIcd.map(r => r.item);
  }

  return results;
};
