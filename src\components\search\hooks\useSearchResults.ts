
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import type { SearchResult } from "../types";
import { toxidromes } from "@/data/toxidromes";
import { useSiteAnalytics } from "@/hooks/useMedicationAnalytics";
import { performMultiTypeFuzzySearch, normalizeForFuzzy } from "../utils/fuzzySearch";

// Função utilitária para normalizar texto (remover acentos e converter para minúsculas)
const normalizeText = (text: string): string => {
  return text
    .toLowerCase()
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "");
};

// Função para criar condições de busca mais flexíveis
const createSearchConditions = (searchTerm: string, normalizedTerm: string, partialSlug: string, slugifiedTerm: string) => {
  const conditions = [
    `name.ilike.%${searchTerm}%`,
    `brands.ilike.%${searchTerm}%`,
    `name.ilike.%${normalizedTerm}%`,
    `brands.ilike.%${normalizedTerm}%`,
    `slug.eq.${slugifiedTerm}`,
    `slug.ilike.%${partialSlug}%`
  ];

  // Adicionar busca por palavras individuais para termos compostos
  const words = normalizedTerm.split(/\s+/).filter(word => word.length >= 3);
  words.forEach(word => {
    conditions.push(`name.ilike.%${word}%`);
    conditions.push(`brands.ilike.%${word}%`);
    conditions.push(`slug.ilike.%${word}%`);
  });

  return conditions.join(',');
};

// Função para criar condições de busca para condutas
const createConductSearchConditions = (searchTerm: string, normalizedTerm: string, partialSlug: string, slugifiedTerm: string) => {
  const conditions = [
    `title.ilike.%${searchTerm}%`,
    `title.ilike.%${normalizedTerm}%`,
    `slug.eq.${slugifiedTerm}`,
    `slug.ilike.%${partialSlug}%`
  ];

  // Adicionar busca por palavras individuais para termos compostos
  const words = normalizedTerm.split(/\s+/).filter(word => word.length >= 3);
  words.forEach(word => {
    conditions.push(`title.ilike.%${word}%`);
    conditions.push(`slug.ilike.%${word}%`);
  });

  return conditions.join(',');
};

export const useSearchResults = (searchTerm: string) => {
  const { toast } = useToast();
  const { trackSearch } = useSiteAnalytics();





  const { data: searchResults = [], isLoading, isFetching } = useQuery({
    queryKey: ['search', searchTerm],
    queryFn: async () => {
      // Verificar se o termo de busca tem pelo menos 3 caracteres
      if (!searchTerm || searchTerm.length < 3) {
        return [];
      }

      try {
        // Normalizar termo de busca para remover acentos e caracteres especiais
        const normalizedTerm = normalizeText(searchTerm);

        // Slugificar o termo para buscar por slug exato
        const slugifiedTerm = searchTerm.toLowerCase().replace(/[^a-z0-9]+/g, "-").replace(/^-|-$/g, "");

        // Criar slug parcial para busca mais flexível (ex: "hidroxido" para "hidroxido-de-magnesio")
        const partialSlug = normalizedTerm.replace(/[^a-z0-9]+/g, "-").replace(/^-|-$/g, "");

        // Busca medicamentos (otimizada com busca de texto completo)
        // Usar condições de busca flexíveis para melhor cobertura
        const medicationConditions = createSearchConditions(searchTerm, normalizedTerm, partialSlug, slugifiedTerm);
        const { data: medications, error: medicationsError } = await supabase
          .from('pedbook_medications')
          .select(`
            id,
            name,
            brands,
            slug,
            pedbook_medication_categories (
              id,
              name
            )
          `)
          .or(medicationConditions)
          .order('name')
          .limit(20); // Limitar resultados para melhor performance

        if (medicationsError) {
          throw medicationsError;
        }

        // Busca categorias (otimizada com LIMIT)
        const { data: categories, error: categoriesError } = await supabase
          .from('pedbook_medication_categories')
          .select('id, name')
          .or(`name.ilike.%${searchTerm}%,name.ilike.%${normalizedTerm}%`)
          .order('name')
          .limit(10); // Limitar categorias

        if (categoriesError) {
          throw categoriesError;
        }

        // Busca CID-10 (otimizada com LIMIT)
        const { data: icd10, error: icd10Error } = await supabase
          .from('unified_cids')
          .select('id, code, name, description')
          .or(`name.ilike.%${searchTerm}%,code.ilike.%${searchTerm}%,name.ilike.%${normalizedTerm}%`)
          .order('code')
          .limit(15); // Limitar CIDs

        if (icd10Error) {
          throw icd10Error;
        }

        // Busca vacinas
        const { data: vaccines, error: vaccinesError } = await supabase
          .from('pedbook_vaccines')
          .select('id, name')
          .ilike('name', `%${searchTerm}%`)
          .order('name')
          .limit(10);

        if (vaccinesError) {
          throw vaccinesError;
        }

        // Busca condutas/resumos (otimizada com LIMIT)
        const conductConditions = createConductSearchConditions(searchTerm, normalizedTerm, partialSlug, slugifiedTerm);
        const { data: conducts, error: conductsError } = await supabase
          .from('pedbook_conducts_summaries')
          .select(`
            id,
            title,
            slug,
            topic_id,
            pedbook_conducts_topics!inner (
              id,
              name,
              slug,
              category_id,
              pedbook_conducts_categories!inner (
                id,
                name,
                slug
              )
            )
          `)
          .or(conductConditions)
          .order('title')
          .limit(15); // Limitar condutas

        if (conductsError) {
          throw conductsError;
        }

        // Busca toxidromes (otimizada com slice)
        const toxidromeResults = toxidromes
          .filter(toxidrome => {
            const searchLower = searchTerm.toLowerCase();
            const nameNormalized = normalizeText(toxidrome.name);
            const antidoteNormalized = normalizeText(toxidrome.antidote);

            const nameMatch = toxidrome.name.toLowerCase().includes(searchLower) || nameNormalized.includes(normalizedTerm);
            const antidoteMatch = toxidrome.antidote.toLowerCase().includes(searchLower) || antidoteNormalized.includes(normalizedTerm);
            return nameMatch || antidoteMatch;
          })
          .slice(0, 10) // Limitar toxidromes
          .map(toxidrome => ({
            id: toxidrome.id,
            name: toxidrome.name,
            type: 'toxidrome' as const,
            path: `/poisonings/${toxidrome.id}`,
            description: `Antídoto: ${toxidrome.antidote}`
          }));

        // Adiciona calculadoras
        const calculators = [
          { id: 'apgar', name: 'Calculadora de Apgar', path: '/calculadoras/apgar' },
          { id: 'rodwell', name: 'Calculadora de Rodwell', path: '/calculadoras/rodwell' },
          { id: 'capurro', name: 'Calculadora de Capurro', path: '/calculadoras/capurro' },
          { id: 'capurro-neuro', name: 'Calculadora de Capurro Neurológico', path: '/calculadoras/capurro-neuro' },
          { id: 'finnegan', name: 'Calculadora de Finnegan', path: '/calculadoras/finnegan' },
          { id: 'gina', name: 'Calculadora GINA', path: '/calculadoras/gina' },
          { id: 'glasgow', name: 'Calculadora de Glasgow', path: '/calculadoras/glasgow' },
          { id: 'bmi', name: 'Calculadora de IMC', path: '/calculadoras/imc' },
        ].filter(calc => {
          const calcNameNormalized = normalizeText(calc.name);
          return calc.name.toLowerCase().includes(searchTerm.toLowerCase()) || calcNameNormalized.includes(normalizedTerm);
        }).slice(0, 8) // Limitar calculadoras
        .map(calc => ({
          ...calc,
          type: 'calculator' as const
        }));

        // Adiciona fluxogramas (lista completa)
        const allFlowcharts = [
          { id: 'dengue', name: 'Fluxograma de Dengue', path: '/flowcharts/dengue', description: 'Manejo de casos suspeitos de dengue' },
          { id: 'asthma', name: 'Fluxograma de Crise Asmática', path: '/flowcharts/asthma', description: 'Manejo de crise asmática em pediatria' },
          { id: 'dka', name: 'Fluxograma de Cetoacidose Diabética', path: '/flowcharts/dka', description: 'Manejo de cetoacidose diabética em pediatria' },
          { id: 'anaphylaxis', name: 'Fluxograma de Anafilaxia', path: '/flowcharts/anaphylaxis', description: 'Manejo de anafilaxia em pediatria' },
          { id: 'seizure', name: 'Fluxograma de Crise Convulsiva', path: '/flowcharts/seizure', description: 'Manejo de crise convulsiva em pediatria' },
          { id: 'pecarn', name: 'Fluxograma PECARN - Trauma Craniano', path: '/flowcharts/pecarn', description: 'Avaliação de trauma craniano em pediatria' },
          { id: 'hydration', name: 'Hidratação Venosa de Manutenção', path: '/calculadoras/hidratacao', description: 'Cálculo da hidratação venosa (Holliday-Segar)' },
          // Fluxogramas de animais peçonhentos com nomes comuns
          { id: 'scorpion', name: 'Fluxograma de Acidente Escorpiônico (Escorpião Amarelo)', path: '/flowcharts/venomous/scorpion', description: 'Manejo de acidentes com escorpião' },
          { id: 'bothropic', name: 'Fluxograma de Acidente Botrópico (Jararaca)', path: '/flowcharts/venomous/bothropic', description: 'Manejo de acidentes com jararaca' },
          { id: 'crotalic', name: 'Fluxograma de Acidente Crotálico (Cascavel)', path: '/flowcharts/venomous/crotalic', description: 'Manejo de acidentes com cascavel' },
          { id: 'elapidic', name: 'Fluxograma de Acidente Elapídico (Coral Verdadeira)', path: '/flowcharts/venomous/elapidic', description: 'Manejo de acidentes com coral verdadeira' },
          { id: 'phoneutria', name: 'Fluxograma de Acidente Fonêutrico (Aranha Armadeira)', path: '/flowcharts/venomous/phoneutria', description: 'Manejo de acidentes com aranha armadeira' },
          { id: 'loxoscelic', name: 'Fluxograma de Acidente Loxoscélico (Aranha Marrom)', path: '/flowcharts/venomous/loxoscelic', description: 'Manejo de acidentes com aranha marrom' }
        ];

        const flowcharts = allFlowcharts.filter(flow => {
          const flowNameNormalized = normalizeText(flow.name);
          const flowDescNormalized = normalizeText(flow.description);
          return flow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                 flowNameNormalized.includes(normalizedTerm) ||
                 flow.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                 flowDescNormalized.includes(normalizedTerm);
        }).slice(0, 10) // Limitar fluxogramas
        .map(flow => ({
          ...flow,
          type: 'flowchart' as const
        }));

        // Adiciona itens de puericultura
        const childcareItems = [
          { id: 'growth', name: 'Curvas de Crescimento', path: '/puericultura/curva-de-crescimento' },
          { id: 'vaccines', name: 'Vacinas', path: '/puericultura/calendario-vacinal' },
          { id: 'formulas', name: 'Fórmulas Infantis', path: '/puericultura/formulas' },
          { id: 'supplementation', name: 'Suplementação', path: '/puericultura/suplementacao-infantil' },
        ].filter(item => {
          const itemNameNormalized = normalizeText(item.name);
          return item.name.toLowerCase().includes(searchTerm.toLowerCase()) || itemNameNormalized.includes(normalizedTerm);
        }).slice(0, 5) // Limitar puericultura
        .map(item => ({
          ...item,
          type: 'childcare' as const
        }));

        // Verificar se precisamos de busca fuzzy (poucos resultados totais)
        const totalResults = (medications?.length || 0) +
                           (categories?.length || 0) +
                           (calculators.length || 0) +
                           (flowcharts.length || 0) +
                           (childcareItems.length || 0) +
                           (conducts?.length || 0) +
                           (vaccines?.length || 0) +
                           (icd10?.length || 0);

        let fuzzyResults: any = {
          medications: [],
          categories: [],
          calculators: [],
          flowcharts: [],
          childcare: [],
          conducts: [],
          vaccines: [],
          icd10: []
        };

        // Se poucos resultados totais, fazer busca fuzzy completa
        if (totalResults < 8) {
          // Buscar dados para fuzzy search
          const [
            { data: allMedications },
            { data: allCategories },
            { data: allConducts },
            { data: allVaccines },
            { data: allIcd10 }
          ] = await Promise.all([
            supabase
              .from('pedbook_medications')
              .select('id, name, brands, slug, pedbook_medication_categories(id, name)')
              .limit(150),
            supabase
              .from('pedbook_medication_categories')
              .select('id, name')
              .limit(50),
            supabase
              .from('pedbook_conducts_summaries')
              .select('id, title as name, slug')
              .limit(100),
            supabase
              .from('pedbook_vaccines')
              .select('id, name')
              .limit(50),
            supabase
              .from('pedbook_icd10')
              .select('id, name, code_range')
              .limit(100)
          ]);

          // Dados estáticos para fuzzy search
          const allCalculators = calculators;
          const allFlowchartsForFuzzy = allFlowcharts; // Usar lista completa para fuzzy
          const allChildcare = childcareItems;

          // Executar busca fuzzy para todos os tipos
          fuzzyResults = performMultiTypeFuzzySearch(
            {
              medications: allMedications || [],
              categories: allCategories || [],
              calculators: allCalculators,
              flowcharts: allFlowchartsForFuzzy,
              childcare: allChildcare,
              conducts: allConducts || [],
              vaccines: allVaccines || [],
              icd10: allIcd10 || []
            },
            searchTerm,
            3 // Máximo 3 resultados fuzzy por tipo
          );

          // Filtrar duplicatas com resultados normais usando IDs únicos
          fuzzyResults.medications = fuzzyResults.medications.filter((item: any) =>
            !medications?.some(med => med.id === item.id)
          );
          fuzzyResults.categories = fuzzyResults.categories.filter((item: any) =>
            !categories?.some(cat => cat.id === item.id)
          );
          fuzzyResults.conducts = fuzzyResults.conducts.filter((item: any) =>
            !conducts?.some(cond => cond.id === item.id)
          );
          fuzzyResults.vaccines = fuzzyResults.vaccines.filter((item: any) =>
            !vaccines?.some(vaccine => vaccine.id === item.id)
          );
          fuzzyResults.icd10 = fuzzyResults.icd10.filter((item: any) =>
            !icd10?.some(icd => icd.id === item.id)
          );

          // Filtrar duplicatas nos dados estáticos também
          fuzzyResults.calculators = fuzzyResults.calculators.filter((item: any) =>
            !calculators.some(calc => calc.id === item.id)
          );
          fuzzyResults.flowcharts = fuzzyResults.flowcharts.filter((item: any) =>
            !flowcharts.some(flow => flow.id === item.id)
          );
          fuzzyResults.childcare = fuzzyResults.childcare.filter((item: any) =>
            !childcareItems.some(child => child.id === item.id)
          );
        }

        const formattedResults: SearchResult[] = [
          // Resultados normais de medicamentos
          ...(medications?.map(med => ({
            id: med.id,
            name: med.name,
            brands: med.brands,
            category: med.pedbook_medication_categories,
            type: 'medication' as const,
            slug: med.slug
          })) || []),

          // Resultados fuzzy de medicamentos
          ...fuzzyResults.medications.map((med: any) => ({
            id: med.id,
            name: med.name,
            brands: med.brands,
            category: med.pedbook_medication_categories,
            type: 'medication' as const,
            slug: med.slug,
            isFuzzyMatch: true
          })),
          // Resultados normais de categorias
          ...(categories?.map(cat => ({
            id: cat.id,
            name: cat.name,
            type: 'category' as const
          })) || []),

          // Resultados fuzzy de categorias
          ...fuzzyResults.categories.map((cat: any) => ({
            id: cat.id,
            name: cat.name,
            type: 'category' as const,
            isFuzzyMatch: true
          })),
          ...toxidromeResults,

          // Resultados normais de calculadoras
          ...calculators,

          // Resultados fuzzy de calculadoras
          ...fuzzyResults.calculators.map((calc: any) => ({
            ...calc,
            isFuzzyMatch: true
          })),

          // Resultados normais de fluxogramas
          ...flowcharts,

          // Resultados fuzzy de fluxogramas
          ...fuzzyResults.flowcharts.map((flow: any) => ({
            ...flow,
            isFuzzyMatch: true
          })),

          // Resultados normais de puericultura
          ...childcareItems,

          // Resultados fuzzy de puericultura
          ...fuzzyResults.childcare.map((child: any) => ({
            ...child,
            isFuzzyMatch: true
          })),
          // Resultados normais de condutas
          ...(conducts?.map(conduct => ({
            id: conduct.id,
            name: conduct.title,
            type: 'conduct' as const,
            path: `/condutas-e-manejos/${conduct.pedbook_conducts_topics.pedbook_conducts_categories.slug}/${conduct.pedbook_conducts_topics.slug}`,
            description: `${conduct.pedbook_conducts_topics.pedbook_conducts_categories.name} > ${conduct.pedbook_conducts_topics.name}`,
            parent_slug: conduct.pedbook_conducts_topics.pedbook_conducts_categories.slug
          })) || []),

          // Resultados fuzzy de condutas
          ...fuzzyResults.conducts.map((conduct: any) => ({
            id: conduct.id,
            name: conduct.name,
            type: 'conduct' as const,
            path: `/condutas-e-manejos/${conduct.slug}`,
            isFuzzyMatch: true
          })),

          // Resultados normais de vacinas
          ...(vaccines?.map(vaccine => ({
            id: vaccine.id,
            name: vaccine.name,
            type: 'vaccine' as const,
            path: `/puericultura/calendario-vacinal`
          })) || []),

          // Resultados fuzzy de vacinas
          ...fuzzyResults.vaccines.map((vaccine: any) => ({
            id: vaccine.id,
            name: vaccine.name,
            type: 'vaccine' as const,
            path: `/puericultura/calendario-vacinal`,
            isFuzzyMatch: true
          })),

          // Resultados normais de CID10
          ...(icd10?.map(icd => ({
            id: icd.id,
            name: icd.name,
            code_range: icd.code,
            description: icd.description,
            type: 'icd10' as const
          })) || []),

          // Resultados fuzzy de CID10
          ...fuzzyResults.icd10.map((icd: any) => ({
            id: icd.id,
            name: icd.name,
            code_range: icd.code_range,
            type: 'icd10' as const,
            isFuzzyMatch: true
          }))
        ];

        // Fazer tracking da busca (não-bloqueante)
        if (formattedResults.length > 0) {
          trackSearch(searchTerm, formattedResults.length, {
            medication_results: medications?.length || 0,
            category_results: categories?.length || 0,
            toxidrome_results: toxidromeResults.length,
            calculator_results: calculators.length,
            flowchart_results: flowcharts.length,
            childcare_results: childcareItems.length,
            conduct_results: conducts?.length || 0,
            vaccine_results: vaccines?.length || 0,
            icd10_results: icd10?.length || 0
          });
        }



        return formattedResults;
      } catch (error) {
        console.error('Erro na busca:', error);
        toast({
          variant: "destructive",
          title: "Erro na busca",
          description: "Não foi possível completar a busca. Tente novamente."
        });
        return [];
      }
    },
    enabled: searchTerm.length >= 3, // Só executa a query quando tiver pelo menos 3 caracteres
    staleTime: 1000 * 60 * 10, // 10 minutos (cache mais longo)
    gcTime: 1000 * 60 * 60, // 1 hora (garbage collection mais longo)
    refetchOnWindowFocus: false,
    refetchOnMount: false, // Não refetch ao montar se já tem cache
    refetchOnReconnect: false, // Não refetch ao reconectar
    retry: 1, // Apenas 1 tentativa em caso de erro
    retryDelay: 1000, // 1 segundo de delay entre tentativas
    networkMode: 'online', // Só executar quando online
    placeholderData: [], // Placeholder data para evitar loading states desnecessários
  });

  return { searchResults, isLoading, isFetching };
};
