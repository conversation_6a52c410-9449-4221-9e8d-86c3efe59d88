
export interface SearchResult {
  id: string;
  name: string;
  brands?: string | null;
  category?: {
    id: string;
    name: string;
  } | null;
  type: 'medication' | 'category' | 'icd10' | 'calculator' | 'flowchart' | 'childcare' | 'toxidrome' | 'conduct' | 'vaccine';
  code_range?: string;
  description?: string | null;
  path?: string;
  slug?: string;
  parent_slug?: string;
  isFuzzyMatch?: boolean; // Flag para indicar busca fuzzy
}
