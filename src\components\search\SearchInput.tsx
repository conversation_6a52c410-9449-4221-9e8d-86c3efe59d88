
import { Search, X } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { RefObject, memo } from "react";
import { Link } from "react-router-dom";

interface SearchInputProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  placeholder?: string;
  onFocus?: () => void;
  customPlaceholder?: string;
  inputRef: RefObject<HTMLInputElement>;
}

export const SearchInput = memo(({
  searchTerm,
  setSearchTerm,
  onFocus,
  customPlaceholder,
  inputRef
}: SearchInputProps) => {
  return (
    <div className="relative group">
      {/* Efeito de sombra para aparência de app */}
      <div className="absolute inset-0 bg-primary/10 rounded-full blur-md opacity-30 group-hover:opacity-50 transition-opacity" />

      {/* Ícone de pesquisa com estilo mais proeminente */}
      <Search
        className="absolute left-4 top-1/2 -translate-y-1/2 text-primary/70 group-hover:text-primary transition-colors"
        size={18}
      />

      {/* Input com cantos mais arredondados para aparência de app */}
      <input
        ref={inputRef}
        type="text"
        placeholder={customPlaceholder || "Buscar medicamentos, condutas, calculadoras..."}
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        onFocus={onFocus}
        onClick={(e) => {
          // Prevenir propagação para evitar conflitos
          e.stopPropagation();
        }}
        className="w-full pl-12 sm:pr-4 pr-12 py-3 rounded-full border-none focus:outline-none focus:ring-2 focus:ring-primary/30
                  bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-md transition-all
                  text-sm text-gray-700 dark:text-gray-200"
      />

      {/* Logo do PedBook no lado direito da barra de pesquisa - apenas em mobile */}
      <Link
        to="/"
        className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 flex items-center justify-center sm:hidden"
      >
        <img
          src="/faviconx.webp"
          alt="PedBook Logo"
          width="48"
          height="48"
          className="h-10 w-10 transition-transform hover:scale-105"
          fetchpriority="high"
          loading="eager"
          decoding="sync"
        />
      </Link>

      {/* Botão de limpar com estilo mais moderno */}
      {searchTerm && (
        <Button
          variant="ghost"
          size="icon"
          className="absolute sm:right-2 right-11 top-1/2 -translate-y-1/2 h-7 w-7 rounded-full bg-gray-100/80 dark:bg-slate-700/80 hover:bg-gray-200 dark:hover:bg-slate-600"
          onClick={() => {
            setSearchTerm('');
            inputRef.current?.focus();
          }}
        >
          <X className="h-3.5 w-3.5 text-gray-500 dark:text-gray-400" />
        </Button>
      )}
    </div>
  );
});
