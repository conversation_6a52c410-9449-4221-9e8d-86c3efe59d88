
<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover" />

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/faviconx.webp" />

    <!-- Preload recursos críticos otimizado para FCP - favicon removido do preload -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style" />

    <!-- Vite gerencia automaticamente os preloads de módulos -->

    <!-- Meta Tags Básicas -->
    <title>PedBook - Calculadora Pediátrica e Doses Pediátricas</title>
    <meta name="description" content="Calculadora pediátrica completa com doses de medicamentos, fluxogramas clínicos e prescrições automatizadas. Ferramenta essencial para pediatras e profissionais da saúde infantil." />
    <meta name="keywords" content="calculadora pediátrica, doses pediátricas, pediatria, fluxogramas clínicos, prescrições médicas" />
    <meta name="author" content="MedUnity" />

    <!-- Preconnect e DNS prefetch otimizado para FCP -->
    <link rel="preconnect" href="https://bxedpdmgvgatjdfxgxij.supabase.co">
    <link rel="preconnect" href="https://www.googletagmanager.com">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://www.gravatar.com">

    <!-- DNS prefetch para recursos secundários -->
    <link rel="dns-prefetch" href="https://cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="https://api.github.com">
    <link rel="dns-prefetch" href="https://unpkg.com">

    <!-- Canonical -->
    <link rel="canonical" href="https://pedb.com.br/" />

    <!-- Open Graph -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://pedb.com.br/" />
    <meta property="og:title" content="PedBook - Calculadora Pediátrica e Doses Pediátricas" />
    <meta property="og:description" content="Calculadora pediátrica e doses pediátricas completas para profissionais da saúde. Faça cálculos de doses, prescrições e acesse fluxogramas clínicos com precisão." />
    <meta property="og:image" content="https://pedb.com.br/faviconx.webp" />
    <meta property="og:author" content="MedUnity" />
    <meta property="og:site_name" content="PedBook" />
    <meta property="og:locale" content="pt_BR" />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content="https://pedb.com.br/" />
    <meta name="twitter:title" content="PedBook - Calculadora Pediátrica e Doses Pediátricas" />
    <meta name="twitter:description" content="Calculadora pediátrica e doses pediátricas completas para profissionais da saúde. Faça cálculos de doses, prescrições e acesse fluxogramas clínicos com precisão." />
    <meta name="twitter:image" content="https://pedb.com.br/faviconx.webp" />

    <!-- PWA/Mobile -->
    <meta name="application-name" content="PedBook" />
    <meta name="theme-color" content="#2563eb" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="PedBook" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="msapplication-TileColor" content="#2563eb" />
    <meta name="msapplication-tap-highlight" content="no" />

    <!-- Robots -->
    <meta name="robots" content="index, follow, max-image-preview:large" />
    <meta name="googlebot" content="index, follow" />

    <!-- CSS Crítico Inline Expandido para melhor FCP -->
    <style>
      /* Reset e base */
      *{box-sizing:border-box}
      body{margin:0;font-family:'Inter',-apple-system,BlinkMacSystemFont,'Segoe UI',sans-serif;line-height:1.6;color:#1f2937;background-color:#fff;font-display:swap}

      /* Layout crítico */
      .main-container{min-height:100vh;display:flex;flex-direction:column}
      .header{position:sticky;top:0;z-index:50;background:rgba(255,255,255,.95);backdrop-filter:blur(10px);border-bottom:1px solid #e5e7eb}

      /* Grid de cards */
      .cards-grid{display:grid;grid-template-columns:repeat(2,1fr);gap:.75rem;max-width:72rem;margin:0 auto;padding:1rem}
      @media (min-width:640px){.cards-grid{grid-template-columns:repeat(3,1fr);gap:1rem}}
      @media (min-width:768px){.cards-grid{grid-template-columns:repeat(4,1fr)}}

      /* Cards básicos */
      .card{position:relative;height:100%;padding:1rem;border-radius:.75rem;background:rgba(255,255,255,.9);border:1px solid #e5e7eb;box-shadow:0 4px 6px -1px rgba(0,0,0,.1);transition:all .3s ease;cursor:pointer}
      .card:hover{transform:translateY(-2px);box-shadow:0 10px 25px -3px rgba(0,0,0,.1)}

      /* Componentes críticos adicionais */
      .btn-primary{background:#0066ff;color:#fff;padding:.75rem 1.5rem;border-radius:.5rem;border:none;cursor:pointer;font-weight:500;transition:background .2s}
      .btn-primary:hover{background:#0052cc}
      .search-container{max-width:48rem;margin:0 auto;padding:1rem}
      .search-input{width:100%;padding:.75rem 1rem;border:1px solid #d1d5db;border-radius:.5rem;font-size:1rem}
      .nav-mobile{position:fixed;bottom:0;left:0;right:0;background:rgba(255,255,255,.95);backdrop-filter:blur(10px);border-top:1px solid #e5e7eb;z-index:50}

      /* Header crítico */
      .header-content{display:flex;align-items:center;justify-content:space-between;max-width:72rem;margin:0 auto;padding:1rem}
      .logo{height:2rem;width:auto}
      .header-nav{display:none}
      @media (min-width:768px){.header-nav{display:flex;gap:1rem}}

      /* Loading states */
      .skeleton{background:linear-gradient(90deg,#f3f4f6 25%,#e5e7eb 50%,#f3f4f6 75%);background-size:200% 100%;animation:loading 1.5s infinite}
      @keyframes loading{0%{background-position:200% 0}100%{background-position:-200% 0}}

      /* Animações otimizadas */
      .fade-in-up{opacity:0;transform:translateY(1rem);animation:fadeInUp .3s ease-out forwards}
      @keyframes fadeInUp{to{opacity:1;transform:translateY(0)}}

      /* CSS Crítico para Campo de Busca - LCP Optimization */
      .search-section{width:100%;max-width:72rem;margin:0 auto;position:relative;z-index:10;display:flex;align-items:center;gap:.5rem;padding:0 .5rem}
      .search-container{flex:1;position:relative}
      .search-input-wrapper{position:relative;display:block}
      .search-input-field{width:100%;padding:.75rem 3rem .75rem 3rem;border-radius:9999px;border:none;outline:none;background:rgba(255,255,255,.9);box-shadow:0 4px 6px -1px rgba(0,0,0,.1);font-size:.875rem;color:#374151;line-height:1.25rem;will-change:auto}
      .search-input-field:focus{box-shadow:0 0 0 2px rgba(37,99,235,.3)}
      .search-icon{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:rgba(37,99,235,.7);width:1.125rem;height:1.125rem}

      /* Otimizações específicas para LCP - Sem layout shift */
      .search-input-field::placeholder{color:#9ca3af;opacity:1}

      /* Remover efeitos pesados que causam layout shift */
      .search-input-wrapper{contain:layout style paint}
      .search-input-field{contain:layout style}



      /* Utilitários de performance - Sem layout shift */
      .gpu-accelerated{backface-visibility:hidden}

      /* CSS Crítico adicional para melhor FCP */
      .min-h-screen{min-height:100vh}
      .flex{display:flex}
      .flex-col{flex-direction:column}
      .relative{position:relative}
      .fixed{position:fixed}
      .inset-0{top:0;right:0;bottom:0;left:0}
      .w-full{width:100%}
      .h-full{height:100%}
      .pointer-events-none{pointer-events:none}
      .z-0{z-index:0}
      .bg-gradient-to-b{background-image:linear-gradient(to bottom,var(--tw-gradient-stops))}
      .animate-pulse{animation:pulse 2s cubic-bezier(0.4,0,0.6,1) infinite}
      @keyframes pulse{0%,100%{opacity:1}50%{opacity:.5}}
    </style>

    <!-- Google Fonts - Carregamento síncrono para evitar layout shift -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
    >

    <!-- KaTeX - Carregamento lazy para melhor FCP -->
    <link
      rel="preload"
      href="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.css"
      as="style"
      onload="this.onload=null;this.rel='stylesheet'"
    >
    <noscript>
      <link
        rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/katex@0.16.0/dist/katex.min.css"
      >
    </noscript>

    <!-- Lazy load CSS não crítico após carregamento da página -->
    <script>
      // Função para carregar CSS não crítico de forma assíncrona
      function loadNonCriticalCSS() {
        // Carregar CSS adicional apenas quando necessário
        const additionalStyles = document.createElement('link');
        additionalStyles.rel = 'stylesheet';
        additionalStyles.href = '/src/styles/non-critical.css';
        additionalStyles.media = 'print';
        additionalStyles.onload = function() {
          this.media = 'all';
        };
        document.head.appendChild(additionalStyles);
      }

      // Carregar CSS não crítico após o carregamento da página
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadNonCriticalCSS);
      } else {
        loadNonCriticalCSS();
      }
    </script>

    <!-- Google Analytics - Carregamento otimizado para performance -->
    <script>
      // SUPER LAZY GTM - Eliminar 132ms de blocking time
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}

      let gtmLoaded = false;

      function loadGoogleAnalytics() {
        if (gtmLoaded) return;
        gtmLoaded = true;

        const script = document.createElement('script');
        script.async = true;
        script.src = 'https://www.googletagmanager.com/gtag/js?id=G-70JXDBJRS7';
        document.head.appendChild(script);

        script.onload = function() {
          gtag('js', new Date());
          gtag('config', 'G-70JXDBJRS7', {
            send_page_view: false,
            transport_type: 'beacon',
            custom_map: {},
            cookie_flags: 'SameSite=None;Secure'
          });
        };
      }

      // Carregar apenas após primeira interação do usuário
      ['click', 'scroll', 'touchstart', 'keydown'].forEach(event => {
        document.addEventListener(event, loadGoogleAnalytics, {
          once: true,
          passive: true
        });
      });

      // Fallback: carregar após 15 segundos se não houver interação
      setTimeout(loadGoogleAnalytics, 15000);
    </script>

    <!-- Schema.org -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "PedBook",
        "description": "Calculadora pediátrica e doses pediátricas completas para profissionais da saúde. Faça cálculos de doses, prescrições e acesse fluxogramas clínicos com precisão.",
        "url": "https://pedb.com.br",
        "applicationCategory": "MedicalApplication",
        "operatingSystem": "Web",
        "version": "1.0",
        "author": {
          "@type": "Organization",
          "name": "MedUnity",
          "url": "https://pedb.com.br"
        },

        "audience": {
          "@type": "MedicalAudience",
          "audienceType": "Pediatras e profissionais da saúde"
        },
        "featureList": [
          "Calculadoras pediátricas",
          "Doses de medicamentos",
          "Fluxogramas clínicos",
          "Prescrições automatizadas",
          "Curvas de crescimento",
          "Avaliação de desenvolvimento"
        ],
        "applicationSubCategory": "Pediatric Medical Calculator",
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": "4.8",
          "ratingCount": "100"
        },
        "provider": {
          "@type": "Organization",
          "name": "MedUnity",
          "url": "https://pedb.com.br"
        },
        "review": {
          "@type": "Review",
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": "5",
            "bestRating": "5"
          },
          "author": {
            "@type": "Organization",
            "name": "MedUnity"
          }
        }
      }
    </script>
  </head>
  <body>


    <div id="root"></div>
    <script>
      window.__INITIAL_STATE__ = '__INITIAL_STATE__'

      // Impedir gestos de pinça para zoom apenas em elementos que não são de texto
      document.addEventListener('gesturestart', function(e) {
        // Permitir gestos em elementos de texto
        const target = e.target;
        if (target.tagName !== 'INPUT' &&
            target.tagName !== 'TEXTAREA' &&
            !target.isContentEditable &&
            !target.classList.contains('selectable-text')) {
          e.preventDefault();
        }
      }, { passive: false });

      document.addEventListener('gesturechange', function(e) {
        // Permitir gestos em elementos de texto
        const target = e.target;
        if (target.tagName !== 'INPUT' &&
            target.tagName !== 'TEXTAREA' &&
            !target.isContentEditable &&
            !target.classList.contains('selectable-text')) {
          e.preventDefault();
        }
      }, { passive: false });



      // Script simples para esconder a barra de navegação quando o teclado é aberto
      (function() {
        // Verificar se o dispositivo é móvel
        var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        if (!isMobile) return;

        // Altura inicial da viewport
        var initialViewportHeight = window.innerHeight;

        // Função para verificar se o teclado está aberto
        function checkKeyboardOpen() {
          // Se a altura atual for significativamente menor que a altura inicial,
          // provavelmente o teclado está aberto
          var keyboardOpen = window.innerHeight < initialViewportHeight * 0.75;

          // Encontrar a barra de navegação
          var navBar = document.querySelector('.mobile-nav-container');
          if (!navBar) return;

          // Esconder ou mostrar a barra de navegação
          if (keyboardOpen) {
            navBar.style.display = 'none';
          } else {
            navBar.style.display = '';
          }
        }

        // Verificar quando a janela é redimensionada (o que acontece quando o teclado é aberto/fechado)
        window.addEventListener('resize', checkKeyboardOpen);

        // Verificar quando um campo de entrada recebe foco
        document.addEventListener('focusin', function(e) {
          if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            // Esconder a barra de navegação imediatamente
            var navBar = document.querySelector('.mobile-nav-container');
            if (navBar) navBar.style.display = 'none';

            // Verificar novamente após um tempo para garantir
            setTimeout(checkKeyboardOpen, 300);
          }
        });

        // Verificar quando um campo de entrada perde foco
        document.addEventListener('focusout', function(e) {
          if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            // Verificar se o teclado foi fechado
            setTimeout(checkKeyboardOpen, 300);
          }
        });
      })();
    </script>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
